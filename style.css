.container {
  padding: 20px;
  border: 1px solid black;
  background: coral;
  display: flex;
  gap: 20px;
  justify-content: space-between;
}

.innerContainer {
  display: flex;
  gap: 4px;
  justify-content: space-between;
  flex-shrink: 0;
  flex-grow: 1;
}

.test1 {
  padding: 5px;
  border: 1px solid black;
  background: darkgreen;
  color: white;
}
.test2 {
  padding: 5px;
  border: 1px solid black;
  background: darkslateblue;
  color: white;
}
.test3 {
  padding: 5px;
  border: 1px solid black;
  background: darkslategrey;
  color: white;
}
